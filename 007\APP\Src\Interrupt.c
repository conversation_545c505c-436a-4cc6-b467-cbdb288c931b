/**
 * @file Interrupt.c
 * @brief 电机控制相关中断处理
 * @version 0.1
 * @date 2025-07-12
 *
 * @copyright Copyright (c) 2025
 *
 */
#include "Interrupt.h"

uint8_t enable_group1_irq = 0;
uint32_t ExISR_Flag; //中断判断标志位

#define ISR_IS_GPIO(X)        (ExISR_Flag & (X))
#define GET_RDR_B_VAL(X)      DL_GPIO_readPins(SPD_READER_B_PORT, (X))
#define CLR_GPIOA_ISR_FLAG(X) DL_GPIO_clearInterruptStatus(GPIOA, (X))

/**
 * @brief Systick时钟中断
 * 
 */
void SysTick_Handler(void)
{
    SysTick_Increasment();
}



/**
 * @brief 外部中断 - 电机编码器中断
 *
 */
void GROUP1_IRQHandler(void)
{
    //判断是不是由GPIOA触发的中断
    if (DL_Interrupt_getPendingGroup(DL_INTERRUPT_GROUP_1) == GPIO_MULTIPLE_GPIOA_INT_IIDX)
    {
        //查看哪个IO进入外部中断
        ExISR_Flag = DL_GPIO_getEnabledInterruptStatus(GPIOA,
                                                       SPD_READER_A_FONT_LEFT_A_PIN | SPD_READER_A_FONT_RIGHT_A_PIN);

        if (ISR_IS_GPIO(SPD_READER_A_FONT_LEFT_A_PIN)) //左前轮编码器
        {
            if (GET_RDR_B_VAL(SPD_READER_B_FONT_LEFT_B_PIN)) (*Motor_Left.Motor_Encoder_Addr)++;
            else (*Motor_Left.Motor_Encoder_Addr)--;
            CLR_GPIOA_ISR_FLAG(SPD_READER_A_FONT_LEFT_A_PIN);
        }

        if (ISR_IS_GPIO(SPD_READER_A_FONT_RIGHT_A_PIN)) //右前轮编码器
        {
            if (GET_RDR_B_VAL(SPD_READER_B_FONT_RIGHT_B_PIN)) (*Motor_Right.Motor_Encoder_Addr)++;
            else (*Motor_Right.Motor_Encoder_Addr)--;
            CLR_GPIOA_ISR_FLAG(SPD_READER_A_FONT_RIGHT_A_PIN);
        }
    }
}

/**
 * @brief 中断初始化
 *
 */
void Interrupt_Init(void)
{
    NVIC_EnableIRQ(1);

    // 清除编码器中断标志位
    CLR_GPIOA_ISR_FLAG(SPD_READER_A_FONT_LEFT_A_PIN);
    CLR_GPIOA_ISR_FLAG(SPD_READER_A_FONT_RIGHT_A_PIN);
}
