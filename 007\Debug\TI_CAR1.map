******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 19:08:43 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001b8d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000023a0  0001dc60  R  X
  SRAM                  20200000   00008000  0000039a  00007c66  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000023a0   000023a0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000021f0   000021f0    r-x .text
  000022b0    000022b0    00000090   00000090    r-- .rodata
  00002340    00002340    00000060   00000060    r-- .cinit
20200000    20200000    0000019a   00000000    rw-
  20200000    20200000    000000f4   00000000    rw- .bss
  202000f4    202000f4    000000a6   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000021f0     
                  000000c0    000001e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000002a0    000001b0     Task.o (.text.Task_Start)
                  00000450    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00000584    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  000006a8    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000007ac    000000f0     Motor.o (.text.Motor_SetDirc)
                  0000089c    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00000978    000000b4     Interrupt.o (.text.GROUP1_IRQHandler)
                  00000a2c    000000b4     Task.o (.text.Task_Add)
                  00000ae0    000000b0     Task_App.o (.text.Task_Motor_PID)
                  00000b90    000000a0     Motor.o (.text.Motor_SetDuty)
                  00000c30    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000cd0    0000009c     Task_App.o (.text.Task_Motor_Test)
                  00000d6c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  00000df8    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00000e84    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00000f08    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00000f8c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000100e    00000002     Task_App.o (.text.Task_IdleFunction)
                  00001010    00000080     Motor.o (.text.Motor_GetSpeed)
                  00001090    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000110c    0000006c     Motor.o (.text.Motor_Start)
                  00001178    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  000011dc    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00001240    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000012a2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000012a4    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00001300    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00001358    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000013a8    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  000013f4    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00001440    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  0000148c    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  000014d8    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00001522    00000002     --HOLE-- [fill = 0]
                  00001524    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0000156c    00000044                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000015b0    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  000015f4    00000044     Task_App.o (.text.Task_Init)
                  00001638    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  0000167a    00000002     --HOLE-- [fill = 0]
                  0000167c    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000016bc    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000016fc    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  0000173c    0000003e     Task.o (.text.Task_CMP)
                  0000177a    00000002     --HOLE-- [fill = 0]
                  0000177c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000017b8    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000017f4    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00001830    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  0000186c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000018a8    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000018e2    00000002     --HOLE-- [fill = 0]
                  000018e4    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000191e    00000002     --HOLE-- [fill = 0]
                  00001920    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00001958    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000198c    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  000019bc    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  000019ec    0000002c     Interrupt.o (.text.Interrupt_Init)
                  00001a18    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001a44    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00001a70    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  00001a9a    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00001ac2    00000002     --HOLE-- [fill = 0]
                  00001ac4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00001aec    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00001b14    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00001b3c    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00001b64    00000028     SysTick.o (.text.SysTick_Increasment)
                  00001b8c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00001bb4    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00001bda    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00001c00    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00001c24    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00001c44    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00001c64    00000020     main.o (.text.main)
                  00001c84    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00001ca2    00000002     --HOLE-- [fill = 0]
                  00001ca4    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00001cc0    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00001cdc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00001cf8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00001d14    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00001d30    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00001d4c    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00001d68    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00001d84    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00001da0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00001dbc    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00001dd8    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00001df4    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00001e10    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00001e28    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00001e40    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00001e58    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00001e70    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00001e88    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00001ea0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00001eb8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00001ed0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00001ee8    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00001f00    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00001f18    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00001f30    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00001f48    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00001f60    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00001f78    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00001f90    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00001fa8    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00001fc0    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00001fd8    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00001ff0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00002008    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00002020    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00002038    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002050    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00002068    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00002080    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00002098    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  000020b0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000020c8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000020e0    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  000020f8    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00002110    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00002128    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  0000213e    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00002154    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000216a    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00002180    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00002194    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000021a8    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000021bc    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000021d0    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000021e4    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000021f8    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000220a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000221c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000222e    00000002     --HOLE-- [fill = 0]
                  00002230    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00002240    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00002250    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00002260    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  0000226c    0000000c     SysTick.o (.text.Sys_GetTick)
                  00002278    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00002282    00000008     Interrupt.o (.text.SysTick_Handler)
                  0000228a    00000002     --HOLE-- [fill = 0]
                  0000228c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00002294    00000006     libc.a : exit.c.obj (.text:abort)
                  0000229a    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000229e    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000022a2    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000022a6    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000022aa    00000006     --HOLE-- [fill = 0]

.cinit     0    00002340    00000060     
                  00002340    00000035     (.cinit..data.load) [load image, compression = lzss]
                  00002375    00000003     --HOLE-- [fill = 0]
                  00002378    0000000c     (__TI_handler_table)
                  00002384    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  0000238c    00000010     (__TI_cinit_table)
                  0000239c    00000004     --HOLE-- [fill = 0]

.rodata    0    000022b0    00000090     
                  000022b0    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000022d8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  000022f0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00002308    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00002312    0000000a     Task_App.o (.rodata.str1.12629676409056169537.1)
                  0000231c    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00002324    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  0000232c    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00002332    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  00002335    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00002337    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00002339    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  0000233b    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000000f4     UNINITIALIZED
                  20200000    000000f0     Task.o (.bss.Task_Schedule)
                  202000f0    00000004     (.common:ExISR_Flag)

.data      0    202000f4    000000a6     UNINITIALIZED
                  202000f4    00000048     Motor.o (.data.Motor_Left)
                  2020013c    00000048     Motor.o (.data.Motor_Right)
                  20200184    00000008     Task_App.o (.data.Motor)
                  2020018c    00000004     Task_App.o (.data.Data_MotorEncoder)
                  20200190    00000004     SysTick.o (.data.delayTick)
                  20200194    00000004     SysTick.o (.data.uwTick)
                  20200198    00000001     Task_App.o (.data.Task_Motor_Test.test_cycle)
                  20200199    00000001     Task.o (.data.Task_Num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             3426   123       0      
       startup_mspm0g350x_ticlang.o   8      192       0      
       main.o                         32     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         3466   315       0      
                                                              
    .\APP\Src\
       Task_App.o                     402    16        13     
       Interrupt.o                    378    0         4      
    +--+------------------------------+------+---------+---------+
       Total:                         780    16        17     
                                                              
    .\BSP\Src\
       Task.o                         674    0         241    
       Motor.o                        704    0         144    
       PID_IQMath.o                   402    0         0      
       SysTick.o                      52     0         8      
    +--+------------------------------+------+---------+---------+
       Total:                         1832   0         393    
                                                              
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388    0         0      
       dl_timer.o                     356    0         0      
       dl_uart.o                      90     0         0      
       dl_dma.o                       76     0         0      
       dl_adc12.o                     64     0         0      
       dl_i2c.o                       38     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1022   0         0      
                                                              
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                      48     0         0      
       _IQNdiv.o                      24     0         0      
       _IQNmpy.o                      24     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         96     0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       qsort.c.obj                    308    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         600    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       mulsf3.S.obj                   140    0         0      
       divsf3.S.obj                   130    0         0      
       comparesf2.S.obj               118    0         0      
       aeabi_fcmp.S.obj               98     0         0      
       fixunsdfsi.S.obj               66     0         0      
       aeabi_uidivmod.S.obj           64     0         0      
       extendsfdf2.S.obj              64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         864    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      89        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   8664   420       922    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000238c records: 2, size/record: 8, table size: 16
	.data: load addr=00002340, load size=00000035 bytes, run addr=202000f4, run size=000000a6 bytes, compression=lzss
	.bss: load addr=00002384, load size=00000008 bytes, run addr=20200000, run size=000000f4 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00002378 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000229b  ADC0_IRQHandler                      
0000229b  ADC1_IRQHandler                      
0000229b  AES_IRQHandler                       
0000229e  C$$EXIT                              
0000229b  CANFD0_IRQHandler                    
0000229b  DAC0_IRQHandler                      
0000167d  DL_ADC12_setClockConfig              
00002279  DL_Common_delayCycles                
000013f5  DL_DMA_initChannel                   
00001bdb  DL_I2C_setClockConfig                
0000089d  DL_SYSCTL_configSYSPLL               
00001179  DL_SYSCTL_setHFCLKSourceHFXTParams   
0000156d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000006a9  DL_Timer_initFourCCPWMMode           
00001dbd  DL_Timer_setCaptCompUpdateMethod     
00002039  DL_Timer_setCaptureCompareOutCtl     
00002241  DL_Timer_setCaptureCompareValue      
00001dd9  DL_Timer_setClockConfig              
00001525  DL_UART_init                         
000021f9  DL_UART_setClockConfig               
0000229b  DMA_IRQHandler                       
2020018c  Data_MotorEncoder                    
0000229b  Default_Handler                      
202000f0  ExISR_Flag                           
0000229b  GROUP0_IRQHandler                    
00000979  GROUP1_IRQHandler                    
0000229f  HOSTexit                             
0000229b  HardFault_Handler                    
0000229b  I2C0_IRQHandler                      
0000229b  I2C1_IRQHandler                      
000019ed  Interrupt_Init                       
20200184  Motor                                
00001011  Motor_GetSpeed                       
202000f4  Motor_Left                           
2020013c  Motor_Right                          
00000b91  Motor_SetDuty                        
0000110d  Motor_Start                          
0000229b  NMI_Handler                          
00001a71  PID_IQ_Init                          
00000585  PID_IQ_Prosc                         
000015b1  PID_IQ_SetParams                     
0000229b  PendSV_Handler                       
0000229b  RTC_IRQHandler                       
000022a3  Reset_Handler                        
0000229b  SPI0_IRQHandler                      
0000229b  SPI1_IRQHandler                      
0000229b  SVC_Handler                          
0000148d  SYSCFG_DL_ADC1_init                  
0000198d  SYSCFG_DL_DMA_CH_RX_init             
000020e1  SYSCFG_DL_DMA_CH_TX_init             
00002261  SYSCFG_DL_DMA_init                   
000000c1  SYSCFG_DL_GPIO_init                  
00001301  SYSCFG_DL_I2C_MPU6050_init           
000011dd  SYSCFG_DL_I2C_OLED_init              
00000d6d  SYSCFG_DL_Motor_PWM_init             
000012a5  SYSCFG_DL_SYSCTL_init                
00002251  SYSCFG_DL_SYSTICK_init               
00000e85  SYSCFG_DL_UART0_init                 
00001a19  SYSCFG_DL_init                       
00000c31  SYSCFG_DL_initPower                  
00002283  SysTick_Handler                      
00001b65  SysTick_Increasment                  
0000226d  Sys_GetTick                          
0000229b  TIMA0_IRQHandler                     
0000229b  TIMA1_IRQHandler                     
0000229b  TIMG0_IRQHandler                     
0000229b  TIMG12_IRQHandler                    
0000229b  TIMG6_IRQHandler                     
0000229b  TIMG7_IRQHandler                     
0000229b  TIMG8_IRQHandler                     
0000220b  TI_memcpy_small                      
00000a2d  Task_Add                             
0000100f  Task_IdleFunction                    
000015f5  Task_Init                            
00000ae1  Task_Motor_PID                       
00000cd1  Task_Motor_Test                      
000002a1  Task_Start                           
0000229b  UART0_IRQHandler                     
0000229b  UART1_IRQHandler                     
0000229b  UART2_IRQHandler                     
0000229b  UART3_IRQHandler                     
000020f9  _IQ24div                             
00002111  _IQ24mpy                             
000019bd  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
0000238c  __TI_CINIT_Base                      
0000239c  __TI_CINIT_Limit                     
0000239c  __TI_CINIT_Warm                      
00002378  __TI_Handler_Table_Base              
00002384  __TI_Handler_Table_Limit             
0000186d  __TI_auto_init_nobinit_nopinit       
00001091  __TI_decompress_lzss                 
0000221d  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000216b  __TI_zero_init_nomemset              
00001639  __aeabi_d2uiz                        
000016fd  __aeabi_f2d                          
00001921  __aeabi_f2iz                         
00001241  __aeabi_fcmpeq                       
0000127d  __aeabi_fcmpge                       
00001291  __aeabi_fcmpgt                       
00001269  __aeabi_fcmple                       
00001255  __aeabi_fcmplt                       
00000f8d  __aeabi_fdiv                         
00000df9  __aeabi_fmul                         
000017f5  __aeabi_i2f                          
000012a3  __aeabi_idiv0                        
0000228d  __aeabi_memcpy                       
0000228d  __aeabi_memcpy4                      
0000228d  __aeabi_memcpy8                      
000016bd  __aeabi_uidiv                        
000016bd  __aeabi_uidivmod                     
ffffffff  __binit__                            
000018a9  __cmpsf2                             
00000f8d  __divsf3                             
000018a9  __eqsf2                              
000016fd  __extendsfdf2                        
00001921  __fixsfsi                            
00001639  __fixunsdfsi                         
000017f5  __floatsisf                          
00001831  __gesf2                              
00001831  __gtsf2                              
000018a9  __lesf2                              
000018a9  __ltsf2                              
UNDEFED   __mpu_init                           
000018e5  __muldsi3                            
00000df9  __mulsf3                             
000018a9  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001b8d  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000022a7  _system_pre_init                     
00002295  abort                                
ffffffff  binit                                
20200190  delayTick                            
00000000  interruptVectors                     
00001c65  main                                 
00000451  qsort                                
20200194  uwTick                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  SYSCFG_DL_GPIO_init                  
00000200  __STACK_SIZE                         
000002a1  Task_Start                           
00000451  qsort                                
00000585  PID_IQ_Prosc                         
000006a9  DL_Timer_initFourCCPWMMode           
0000089d  DL_SYSCTL_configSYSPLL               
00000979  GROUP1_IRQHandler                    
00000a2d  Task_Add                             
00000ae1  Task_Motor_PID                       
00000b91  Motor_SetDuty                        
00000c31  SYSCFG_DL_initPower                  
00000cd1  Task_Motor_Test                      
00000d6d  SYSCFG_DL_Motor_PWM_init             
00000df9  __aeabi_fmul                         
00000df9  __mulsf3                             
00000e85  SYSCFG_DL_UART0_init                 
00000f8d  __aeabi_fdiv                         
00000f8d  __divsf3                             
0000100f  Task_IdleFunction                    
00001011  Motor_GetSpeed                       
00001091  __TI_decompress_lzss                 
0000110d  Motor_Start                          
00001179  DL_SYSCTL_setHFCLKSourceHFXTParams   
000011dd  SYSCFG_DL_I2C_OLED_init              
00001241  __aeabi_fcmpeq                       
00001255  __aeabi_fcmplt                       
00001269  __aeabi_fcmple                       
0000127d  __aeabi_fcmpge                       
00001291  __aeabi_fcmpgt                       
000012a3  __aeabi_idiv0                        
000012a5  SYSCFG_DL_SYSCTL_init                
00001301  SYSCFG_DL_I2C_MPU6050_init           
000013f5  DL_DMA_initChannel                   
0000148d  SYSCFG_DL_ADC1_init                  
00001525  DL_UART_init                         
0000156d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000015b1  PID_IQ_SetParams                     
000015f5  Task_Init                            
00001639  __aeabi_d2uiz                        
00001639  __fixunsdfsi                         
0000167d  DL_ADC12_setClockConfig              
000016bd  __aeabi_uidiv                        
000016bd  __aeabi_uidivmod                     
000016fd  __aeabi_f2d                          
000016fd  __extendsfdf2                        
000017f5  __aeabi_i2f                          
000017f5  __floatsisf                          
00001831  __gesf2                              
00001831  __gtsf2                              
0000186d  __TI_auto_init_nobinit_nopinit       
000018a9  __cmpsf2                             
000018a9  __eqsf2                              
000018a9  __lesf2                              
000018a9  __ltsf2                              
000018a9  __nesf2                              
000018e5  __muldsi3                            
00001921  __aeabi_f2iz                         
00001921  __fixsfsi                            
0000198d  SYSCFG_DL_DMA_CH_RX_init             
000019bd  _IQ24toF                             
000019ed  Interrupt_Init                       
00001a19  SYSCFG_DL_init                       
00001a71  PID_IQ_Init                          
00001b65  SysTick_Increasment                  
00001b8d  _c_int00_noargs                      
00001bdb  DL_I2C_setClockConfig                
00001c65  main                                 
00001dbd  DL_Timer_setCaptCompUpdateMethod     
00001dd9  DL_Timer_setClockConfig              
00002039  DL_Timer_setCaptureCompareOutCtl     
000020e1  SYSCFG_DL_DMA_CH_TX_init             
000020f9  _IQ24div                             
00002111  _IQ24mpy                             
0000216b  __TI_zero_init_nomemset              
000021f9  DL_UART_setClockConfig               
0000220b  TI_memcpy_small                      
0000221d  __TI_decompress_none                 
00002241  DL_Timer_setCaptureCompareValue      
00002251  SYSCFG_DL_SYSTICK_init               
00002261  SYSCFG_DL_DMA_init                   
0000226d  Sys_GetTick                          
00002279  DL_Common_delayCycles                
00002283  SysTick_Handler                      
0000228d  __aeabi_memcpy                       
0000228d  __aeabi_memcpy4                      
0000228d  __aeabi_memcpy8                      
00002295  abort                                
0000229b  ADC0_IRQHandler                      
0000229b  ADC1_IRQHandler                      
0000229b  AES_IRQHandler                       
0000229b  CANFD0_IRQHandler                    
0000229b  DAC0_IRQHandler                      
0000229b  DMA_IRQHandler                       
0000229b  Default_Handler                      
0000229b  GROUP0_IRQHandler                    
0000229b  HardFault_Handler                    
0000229b  I2C0_IRQHandler                      
0000229b  I2C1_IRQHandler                      
0000229b  NMI_Handler                          
0000229b  PendSV_Handler                       
0000229b  RTC_IRQHandler                       
0000229b  SPI0_IRQHandler                      
0000229b  SPI1_IRQHandler                      
0000229b  SVC_Handler                          
0000229b  TIMA0_IRQHandler                     
0000229b  TIMA1_IRQHandler                     
0000229b  TIMG0_IRQHandler                     
0000229b  TIMG12_IRQHandler                    
0000229b  TIMG6_IRQHandler                     
0000229b  TIMG7_IRQHandler                     
0000229b  TIMG8_IRQHandler                     
0000229b  UART0_IRQHandler                     
0000229b  UART1_IRQHandler                     
0000229b  UART2_IRQHandler                     
0000229b  UART3_IRQHandler                     
0000229e  C$$EXIT                              
0000229f  HOSTexit                             
000022a3  Reset_Handler                        
000022a7  _system_pre_init                     
00002378  __TI_Handler_Table_Base              
00002384  __TI_Handler_Table_Limit             
0000238c  __TI_CINIT_Base                      
0000239c  __TI_CINIT_Limit                     
0000239c  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202000f0  ExISR_Flag                           
202000f4  Motor_Left                           
2020013c  Motor_Right                          
20200184  Motor                                
2020018c  Data_MotorEncoder                    
20200190  delayTick                            
20200194  uwTick                               
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[156 symbols]
