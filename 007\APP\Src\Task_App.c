/**
 * @file Task_App.c
 * @brief 电机控制任务实现层
 * @version 0.1
 * @date 2025-07-12
 *
 * @copyright Copyright (c) 2025
 *
 */
#include "Task_App.h"

/*Data Motor*/
_iq Data_Motor_TarSpeed = _IQ(30); //目标基础速度
int16_t Data_MotorEncoder[2] = {0}; //电机的编码值
MOTOR_Def_t *Motor[2] = {&Motor_Left, &Motor_Right}; //电机实例

// 电机测试状态
typedef enum {
    MOTOR_TEST_STOP = 0,
    MOTOR_TEST_FORWARD,
    MOTOR_TEST_BACKWARD,
    MOTOR_TEST_LEFT_TURN,
    MOTOR_TEST_RIGHT_TURN
} MotorTestState_t;

static MotorTestState_t motor_test_state = MOTOR_TEST_FORWARD;
static uint32_t test_start_time = 0;

void Task_Motor_PID(void *para);
void Task_Motor_Test(void *para);

void Task_Init(void)
{
    Motor_Start(); //开启电机
    Interrupt_Init(); //中断初始化

    // 添加电机PID控制任务和测试任务
    Task_Add("Motor", Task_Motor_PID, 50, NULL, 0);
    Task_Add("MotorTest", Task_Motor_Test, 2000, NULL, 1); // 2秒切换一次状态
}

//空闲任务函数
void Task_IdleFunction(void)
{
    // 空闲时可以执行一些低优先级任务
}

//电机PID调控 50ms
void Task_Motor_PID(void *para)
{
    //获取电机速度
    for (uint8_t i = 0; i < 2; i++)
    {
        Motor_GetSpeed(Motor[i], 50);
    }

    //PID 计算
    for (uint8_t i = 0; i < 2; i++)
    {
        PID_IQ_Prosc(&Motor[i]->Motor_PID_Instance);
    }

    // 设置电机PWM输出
    for (uint8_t i = 0; i < 2; i++)
    {
        float output = _IQtoF(Motor[i]->Motor_PID_Instance.Out);
        Motor_SetDuty(Motor[i], output);
    }
}

//电机测试任务 2000ms
void Task_Motor_Test(void *para)
{
    static uint8_t test_cycle = 0;

    switch (test_cycle % 5)
    {
        case 0: // 停止
            Motor_Left.Motor_PID_Instance.Target = _IQ(0);
            Motor_Right.Motor_PID_Instance.Target = _IQ(0);
            break;

        case 1: // 前进
            Motor_Left.Motor_PID_Instance.Target = _IQ(20);
            Motor_Right.Motor_PID_Instance.Target = _IQ(20);
            break;

        case 2: // 后退
            Motor_Left.Motor_PID_Instance.Target = _IQ(-20);
            Motor_Right.Motor_PID_Instance.Target = _IQ(-20);
            break;

        case 3: // 左转
            Motor_Left.Motor_PID_Instance.Target = _IQ(10);
            Motor_Right.Motor_PID_Instance.Target = _IQ(30);
            break;

        case 4: // 右转
            Motor_Left.Motor_PID_Instance.Target = _IQ(30);
            Motor_Right.Motor_PID_Instance.Target = _IQ(10);
            break;
    }

    test_cycle++;
}

